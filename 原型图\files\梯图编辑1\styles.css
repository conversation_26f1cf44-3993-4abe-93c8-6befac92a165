﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-0px;
  width:1024px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u72 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u73_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:768px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u73 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:768px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u73 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u73_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u74 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u75_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u75 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u75 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u75_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u76 {
  border-width:0px;
  position:absolute;
  left:113px;
  top:11px;
  width:30px;
  height:30px;
  display:flex;
  transition:none;
}
#u76 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u76_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u76_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u77_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:30px;
  text-align:center;
}
#u77 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:30px;
  text-align:center;
}
#u77 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u77_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u78 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:25px;
  width:57px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-62.19271366293822deg);
  -moz-transform:rotate(-62.19271366293822deg);
  -ms-transform:rotate(-62.19271366293822deg);
  transform:rotate(-62.19271366293822deg);
  transition:none;
}
#u78 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u78_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:58px;
  height:2px;
}
#u78_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u79 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u80_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u80 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:703px;
  width:1024px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u80 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u80_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u81_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u81 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u81 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u81_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u82_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u82 {
  border-width:0px;
  position:absolute;
  left:132px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u82 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u82_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u83_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u83 {
  border-width:0px;
  position:absolute;
  left:227px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u83 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u83_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u84_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u84 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u84 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u84_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u85_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u85 {
  border-width:0px;
  position:absolute;
  left:417px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u85 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u85_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u86_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u86 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u86 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u86_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u87_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u87 {
  border-width:0px;
  position:absolute;
  left:607px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u87 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u87_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u88_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u88 {
  border-width:0px;
  position:absolute;
  left:702px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u88 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u88_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u89_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u89 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u89 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u89_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u90_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u90 {
  border-width:0px;
  position:absolute;
  left:892px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u90 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u90_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u91_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u91 {
  border-width:0px;
  position:absolute;
  left:987px;
  top:703px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u91 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u91_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u92_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u92 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:703px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u92 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u92_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u93 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u94_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:990px;
  height:621px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.9921568627450981);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  filter:drop-shadow(none);
  transition:none;
  color:transparent;
}
#u94 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:66px;
  width:990px;
  height:621px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:transparent;
}
#u94 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u94_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u95 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:96px;
  width:990px;
  height:1px;
  display:flex;
  transition:none;
}
#u95 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u95_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:991px;
  height:2px;
}
#u95_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u96_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:18px;
  text-align:center;
}
#u96 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:72px;
  width:72px;
  height:21px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:18px;
  text-align:center;
}
#u96 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u96_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u97 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:96px;
  width:851px;
  height:591px;
  display:flex;
  transition:none;
}
#u97 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u97_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:851px;
  height:591px;
}
#u97_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
