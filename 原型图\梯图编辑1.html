﻿<!DOCTYPE html>
<html>
  <head>
    <title>梯图编辑1</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/梯图编辑1/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/梯图编辑1/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- 梯图编辑1 (Group) -->
      <div id="u72" class="ax_default" data-label="梯图编辑1" data-left="0" data-top="0" data-width="1024" data-height="768" layer-opacity="1">

        <!-- Unnamed (Rectangle) -->
        <div id="u73" class="ax_default box_1 error transition notrs">
          <div id="u73_div" class="error"></div>
          <div id="u73_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u74" class="ax_default" data-left="0" data-top="0" data-width="1024" data-height="50.94211680070307" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u75" class="ax_default box_1 error transition notrs">
            <div id="u75_div" class="error"></div>
            <div id="u75_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Placeholder) -->
          <div id="u76" class="ax_default placeholder error transition notrs">
            <svg data="images/梯图编辑1/u76.svg" id="u76_img" class="img generatedImage">

  <defs>
    <pattern id="u76_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u76_img_cl11">
      <path d="M 0 30  L 0 0  L 30 0  L 30 30  L 0 30  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -113 -11 )">
    <path d="M 0 30  L 0 0  L 30 0  L 30 30  L 0 30  Z " fill-rule="nonzero" fill="rgba(0, 0, 0, 0)" stroke="none" transform="matrix(1 0 0 1 113 11 )" class="fill" />
    <path d="M 0 30  L 0 0  L 30 0  L 30 30  L 0 30  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 113 11 )" class="stroke" mask="url(#u76_img_cl11)" />
    <path d="M 29.646446609406727 0.35355339059327373  L 0.35355339059327373 29.646446609406727  M 0.35355339059327373 0.35355339059327373  L 29.646446609406727 29.646446609406727  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 113 11 )" class="stroke" />
  </g>
            </svg>
            <div id="u76_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u77" class="ax_default label error transition notrs">
            <div id="u77_div" class="error"></div>
            <div id="u77_text" class="text ">
              <p><span>Edit</span></p>
            </div>
          </div>

          <!-- Unnamed (Line) -->
          <div id="u78" class="ax_default line error transition notrs">
            <svg data="images/三级菜单/u22.svg" id="u78_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -66 -25 )">
    <path d="M 0 0.5  L 57 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 66 25 )" class="stroke" />
  </g>
            </svg>
            <div id="u78_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u79" class="ax_default" data-left="0" data-top="703" data-width="1024" data-height="65" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u80" class="ax_default box_1 error transition notrs">
            <div id="u80_div" class="error"></div>
            <div id="u80_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u81" class="ax_default button error transition notrs">
            <div id="u81_div" class="error"></div>
            <div id="u81_text" class="text ">
              <p><span>程序列表</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u82" class="ax_default button error transition notrs">
            <div id="u82_div" class="error"></div>
            <div id="u82_text" class="text ">
              <p><span>直线</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u83" class="ax_default button error transition notrs">
            <div id="u83_div" class="error"></div>
            <div id="u83_text" class="text ">
              <p><span>竖线</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u84" class="ax_default button error transition notrs">
            <div id="u84_div" class="error"></div>
            <div id="u84_text" class="text ">
              <p><span>触点</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u85" class="ax_default button error transition notrs">
            <div id="u85_div" class="error"></div>
            <div id="u85_text" class="text ">
              <p><span>线圈</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u86" class="ax_default button error transition notrs">
            <div id="u86_div" class="error"></div>
            <div id="u86_text" class="text ">
              <p><span>比较指令</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u87" class="ax_default button error transition notrs">
            <div id="u87_div" class="error"></div>
            <div id="u87_text" class="text ">
              <p><span>功能指令</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u88" class="ax_default button error transition notrs">
            <div id="u88_div" class="error"></div>
            <div id="u88_text" class="text ">
              <p><span>删除元件</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u89" class="ax_default button error transition notrs">
            <div id="u89_div" class="error"></div>
            <div id="u89_text" class="text ">
              <p><span>删除竖线</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u90" class="ax_default button error transition notrs">
            <div id="u90_div" class="error"></div>
            <div id="u90_text" class="text ">
              <p><span>参数设置</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u91" class="ax_default button error transition notrs">
            <div id="u91_div" class="error"></div>
            <div id="u91_text" class="text ">
              <p><span>&gt;</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u92" class="ax_default button error transition notrs">
            <div id="u92_div" class="error"></div>
            <div id="u92_text" class="text ">
              <p><span>&lt;</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u93" class="ax_default" data-left="17" data-top="66" data-width="990" data-height="621" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u94" class="ax_default box_1 error transition notrs">
            <div id="u94_div" class="error"></div>
            <div id="u94_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Line) -->
          <div id="u95" class="ax_default line error transition notrs">
            <svg data="images/三级菜单/u25.svg" id="u95_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -16 -95 )">
    <path d="M 0 0.5  L 990 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 16 95 )" class="stroke" />
  </g>
            </svg>
            <div id="u95_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u96" class="ax_default label error transition notrs">
            <div id="u96_div" class="error"></div>
            <div id="u96_text" class="text ">
              <p><span>题图编辑</span></p>
            </div>
          </div>

          <!-- Unnamed (Image) -->
          <div id="u97" class="ax_default image error transition notrs">
            <img id="u97_img" class="img " src="images/梯图编辑1/u97.png"/>
            <div id="u97_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
