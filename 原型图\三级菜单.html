﻿<!DOCTYPE html>
<html>
  <head>
    <title>三级菜单</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/三级菜单/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/三级菜单/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- 模板 (Group) -->
      <div id="u2" class="ax_default" data-label="模板" data-left="0" data-top="0" data-width="1024" data-height="769" layer-opacity="1">

        <!-- Unnamed (Rectangle) -->
        <div id="u3" class="ax_default box_1 error transition notrs">
          <div id="u3_div" class="error"></div>
          <div id="u3_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u4" class="ax_default" data-left="0" data-top="0" data-width="1024" data-height="50" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u5" class="ax_default box_1 error transition notrs">
            <div id="u5_div" class="error"></div>
            <div id="u5_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Placeholder) -->
          <div id="u6" class="ax_default placeholder error transition notrs">
            <svg data="images/三级菜单/u6.svg" id="u6_img" class="img generatedImage">

  <defs>
    <pattern id="u6_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u6_img_cl9">
      <path d="M 0 30  L 0 0  L 30 0  L 30 30  L 0 30  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -113 -11 )">
    <path d="M 0 30  L 0 0  L 30 0  L 30 30  L 0 30  Z " fill-rule="nonzero" fill="rgba(0, 0, 0, 0)" stroke="none" transform="matrix(1 0 0 1 113 11 )" class="fill" />
    <path d="M 0 30  L 0 0  L 30 0  L 30 30  L 0 30  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 113 11 )" class="stroke" mask="url(#u6_img_cl9)" />
    <path d="M 29.646446609406727 0.35355339059327373  L 0.35355339059327373 29.646446609406727  M 0.35355339059327373 0.35355339059327373  L 29.646446609406727 29.646446609406727  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 113 11 )" class="stroke" />
  </g>
            </svg>
            <div id="u6_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u7" class="ax_default label error transition notrs">
            <div id="u7_div" class="error"></div>
            <div id="u7_text" class="text ">
              <p><span>Edit</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u8" class="ax_default" data-left="0" data-top="0.057883199296924204" data-width="1024" data-height="767.942116800703" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u9" class="ax_default box_1 error transition notrs">
            <div id="u9_div" class="error"></div>
            <div id="u9_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u10" class="ax_default button error transition notrs">
            <div id="u10_div" class="error"></div>
            <div id="u10_text" class="text ">
              <p><span>梯图信息</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u11" class="ax_default button error transition notrs">
            <div id="u11_div" class="error"></div>
            <div id="u11_text" class="text ">
              <p><span>梯图编辑</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u12" class="ax_default button error transition notrs">
            <div id="u12_div" class="error"></div>
            <div id="u12_text" class="text ">
              <p><span>符号表</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u13" class="ax_default button error transition notrs">
            <div id="u13_div" class="error"></div>
            <div id="u13_text" class="text ">
              <p><span>IO对照表</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u14" class="ax_default button error transition notrs">
            <div id="u14_div" class="error"></div>
            <div id="u14_text" class="text ">
              <p><span>IO监控</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u15" class="ax_default button error transition notrs">
            <div id="u15_div" class="error"></div>
            <div id="u15_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u16" class="ax_default button error transition notrs">
            <div id="u16_div" class="error"></div>
            <div id="u16_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u17" class="ax_default button error transition notrs">
            <div id="u17_div" class="error"></div>
            <div id="u17_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u18" class="ax_default button error transition notrs">
            <div id="u18_div" class="error"></div>
            <div id="u18_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u19" class="ax_default button error transition notrs">
            <div id="u19_div" class="error"></div>
            <div id="u19_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u20" class="ax_default button error transition notrs">
            <div id="u20_div" class="error"></div>
            <div id="u20_text" class="text ">
              <p><span>&gt;</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u21" class="ax_default button error transition notrs">
            <div id="u21_div" class="error"></div>
            <div id="u21_text" class="text ">
              <p><span>&lt;</span></p>
            </div>
          </div>

          <!-- Unnamed (Line) -->
          <div id="u22" class="ax_default line error transition notrs">
            <svg data="images/三级菜单/u22.svg" id="u22_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -66 -25 )">
    <path d="M 0 0.5  L 57 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 66 25 )" class="stroke" />
  </g>
            </svg>
            <div id="u22_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u23" class="ax_default" data-left="16" data-top="65" data-width="990" data-height="621" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u24" class="ax_default box_1 error transition notrs">
            <div id="u24_div" class="error"></div>
            <div id="u24_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Line) -->
          <div id="u25" class="ax_default line error transition notrs">
            <svg data="images/三级菜单/u25.svg" id="u25_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -16 -95 )">
    <path d="M 0 0.5  L 990 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 16 95 )" class="stroke" />
  </g>
            </svg>
            <div id="u25_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u26" class="ax_default label error transition notrs">
            <div id="u26_div" class="error"></div>
            <div id="u26_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
