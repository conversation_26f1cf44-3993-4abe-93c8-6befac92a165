﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,j,k,_(l,m,n,o)),p,[],q,_(h,r),s,[t],u,_(v,w,x,y,g,z,A,_(),B,[],C,_(D,E,F,G,H,_(I,J,K,L),M,null,N,_(O,P,Q,P),R,S,T,null,U,V,W,X,Y,Z,ba,V,bb,bc,bd,_(I,J,K,be),bf,V,bg,bc,bh,_(bi,bj,bk,bl,bm,bl,bn,bl,bo,o,K,_(bp,bq,br,bq,bs,bq,bt,bu)),k,_(l,o,n,o),bv,bw),bx,_(),by,_(),bz,_(bA,[_(bB,bC,bD,h,bE,bF,x,bG,bH,bG,bI,bJ,C,_(Y,bK,bL,bM,bN,bO,bP,bQ,bR,_(I,J,K,bS),D,bT,k,_(l,m,n,bU),M,_(bV,bW,l,m,n,bU)),bx,_(),bX,_(),bY,_(bZ,ca),cb,bj,cc,bj)])),cd,_(),ce,_(cf,_(cg,ch)));}; 
var b="url",c="二级菜单.html",d="generationDate",e=new Date(1751876639734.0056),f="defaultAdaptiveView",g="name",h="",i="cursor",j=1,k="size",l="width",m=1024,n="height",o=0,p="adaptiveViews",q="sketchKeys",r="s0",s="variables",t="OnLoadVariable",u="page",v="packageId",w="fc466a21c65048d49b1fcbd57d24fbf5",x="type",y="Axure:Page",z="二级菜单",A="notes",B="annotations",C="style",D="baseStyle",E="627587b6038d43cca051c114ac41ad32",F="pageAlignment",G="center",H="fill",I="fillType",J="solid",K="color",L=0xFFFFFFFF,M="image",N="imageAlignment",O="horizontal",P="near",Q="vertical",R="imageRepeat",S="auto",T="favicon",U="sketchFactor",V="0",W="colorStyle",X="appliedColor",Y="fontName",Z="Applied font",ba="borderWidth",bb="borderVisibility",bc="top right bottom left",bd="borderFill",be=0xFF797979,bf="cornerRadius",bg="cornerVisibility",bh="outerShadow",bi="on",bj=false,bk="offsetX",bl=5,bm="offsetY",bn="blurRadius",bo="spread",bp="r",bq=0,br="g",bs="b",bt="a",bu=0.34901960784313724,bv="pageCursor",bw="touch",bx="adaptiveStyles",by="interactionMap",bz="diagram",bA="objects",bB="id",bC="bb8d17386c7f405fb7067053b8d4ca7b",bD="label",bE="friendlyType",bF="Image",bG="imageBox",bH="styleType",bI="visible",bJ=true,bK="\"Arial Normal\", \"Arial\", sans-serif",bL="fontWeight",bM="400",bN="fontStyle",bO="normal",bP="fontStretch",bQ="5",bR="foreGroundFill",bS=0xFF333333,bT="75a91ee5b9d042cfa01b8d565fe289c0",bU=768,bV="path",bW="../../images/二级菜单/u1.png",bX="imageOverrides",bY="images",bZ="normal~",ca="images/二级菜单/u1.png",cb="autoFitWidth",cc="autoFitHeight",cd="masters",ce="objectPaths",cf="bb8d17386c7f405fb7067053b8d4ca7b",cg="scriptId",ch="u1";
return _creator();
})());