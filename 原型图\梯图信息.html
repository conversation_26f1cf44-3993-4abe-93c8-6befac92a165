﻿<!DOCTYPE html>
<html>
  <head>
    <title>梯图信息</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/梯图信息/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/梯图信息/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- 梯图信息 (Group) -->
      <div id="u27" class="ax_default" data-label="梯图信息" data-left="0" data-top="0" data-width="1024" data-height="768" layer-opacity="1">

        <!-- Unnamed (Rectangle) -->
        <div id="u28" class="ax_default box_1 error transition notrs">
          <div id="u28_div" class="error"></div>
          <div id="u28_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u29" class="ax_default" data-left="0" data-top="0" data-width="1024" data-height="50" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u30" class="ax_default box_1 error transition notrs">
            <div id="u30_div" class="error"></div>
            <div id="u30_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Placeholder) -->
          <div id="u31" class="ax_default placeholder error transition notrs">
            <svg data="images/梯图信息/u31.svg" id="u31_img" class="img generatedImage">

  <defs>
    <pattern id="u31_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u31_img_cl10">
      <path d="M 0 30  L 0 0  L 30 0  L 30 30  L 0 30  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -113 -11 )">
    <path d="M 0 30  L 0 0  L 30 0  L 30 30  L 0 30  Z " fill-rule="nonzero" fill="rgba(242, 242, 242, 1)" stroke="none" transform="matrix(1 0 0 1 113 11 )" class="fill" />
    <path d="M 0 30  L 0 0  L 30 0  L 30 30  L 0 30  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 113 11 )" class="stroke" mask="url(#u31_img_cl10)" />
    <path d="M 29.646446609406727 0.35355339059327373  L 0.35355339059327373 29.646446609406727  M 0.35355339059327373 0.35355339059327373  L 29.646446609406727 29.646446609406727  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 113 11 )" class="stroke" />
  </g>
            </svg>
            <div id="u31_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u32" class="ax_default label error transition notrs">
            <div id="u32_div" class="error"></div>
            <div id="u32_text" class="text ">
              <p><span>Edit</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u33" class="ax_default" data-left="17" data-top="66" data-width="990" data-height="621" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u34" class="ax_default box_1 error transition notrs">
            <div id="u34_div" class="error"></div>
            <div id="u34_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Line) -->
          <div id="u35" class="ax_default line error transition notrs">
            <svg data="images/三级菜单/u25.svg" id="u35_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -16 -95 )">
    <path d="M 0 0.5  L 990 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 16 95 )" class="stroke" />
  </g>
            </svg>
            <div id="u35_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u36" class="ax_default label error transition notrs">
            <div id="u36_div" class="error"></div>
            <div id="u36_text" class="text ">
              <p><span>梯图信息</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u37" class="ax_default" data-left="0" data-top="0.057883199296924204" data-width="1024" data-height="767.942116800703" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u38" class="ax_default box_1 error transition notrs">
            <div id="u38_div" class="error"></div>
            <div id="u38_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u39" class="ax_default button error transition notrs">
            <div id="u39_div" class="error"></div>
            <div id="u39_text" class="text ">
              <p><span>新建</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u40" class="ax_default button error transition notrs">
            <div id="u40_div" class="error"></div>
            <div id="u40_text" class="text ">
              <p><span>打开</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u41" class="ax_default button error transition notrs">
            <div id="u41_div" class="error"></div>
            <div id="u41_text" class="text ">
              <p><span>保存</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u42" class="ax_default button error transition notrs">
            <div id="u42_div" class="error"></div>
            <div id="u42_text" class="text ">
              <p><span>另存为</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u43" class="ax_default button error transition notrs">
            <div id="u43_div" class="error"></div>
            <div id="u43_text" class="text ">
              <p><span>重命名</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u44" class="ax_default button error transition notrs">
            <div id="u44_div" class="error"></div>
            <div id="u44_text" class="text ">
              <p><span>启动</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u45" class="ax_default button error transition notrs">
            <div id="u45_div" class="error"></div>
            <div id="u45_text" class="text ">
              <p><span>停止</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u46" class="ax_default button error transition notrs">
            <div id="u46_div" class="error"></div>
            <div id="u46_text" class="text ">
              <p><span>在线调试</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u47" class="ax_default button error transition notrs">
            <div id="u47_div" class="error"></div>
            <div id="u47_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u48" class="ax_default button error transition notrs">
            <div id="u48_div" class="error"></div>
            <div id="u48_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u49" class="ax_default button error transition notrs">
            <div id="u49_div" class="error"></div>
            <div id="u49_text" class="text ">
              <p><span>&gt;</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u50" class="ax_default button error transition notrs">
            <div id="u50_div" class="error"></div>
            <div id="u50_text" class="text ">
              <p><span>&lt;</span></p>
            </div>
          </div>

          <!-- Unnamed (Line) -->
          <div id="u51" class="ax_default line error transition notrs">
            <svg data="images/三级菜单/u22.svg" id="u51_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -66 -25 )">
    <path d="M 0 0.5  L 57 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 66 25 )" class="stroke" />
  </g>
            </svg>
            <div id="u51_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u52" class="ax_default label error transition notrs">
            <div id="u52_div" class="error"></div>
            <div id="u52_text" class="text ">
              <p><span>名称</span></p>
            </div>
          </div>

          <!-- Unnamed (Text field) -->
          <div id="u53" class="ax_default text_field error transition notrs">
            <div id="u53_div" class="error"></div>
            <input id="u53_input" type="text" value="5080.plc" class="u53_input"/>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u54" class="ax_default label error transition notrs">
            <div id="u54_div" class="error"></div>
            <div id="u54_text" class="text ">
              <p><span>作者</span></p>
            </div>
          </div>

          <!-- Unnamed (Text field) -->
          <div id="u55" class="ax_default text_field error transition notrs">
            <div id="u55_div" class="error"></div>
            <input id="u55_input" type="text" value="王小明" class="u55_input"/>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u56" class="ax_default label error transition notrs">
            <div id="u56_div" class="error"></div>
            <div id="u56_text" class="text ">
              <p><span>版本</span></p>
            </div>
          </div>

          <!-- Unnamed (Text field) -->
          <div id="u57" class="ax_default text_field error transition notrs">
            <div id="u57_div" class="error"></div>
            <input id="u57_input" type="text" value="v1.0" class="u57_input"/>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u58" class="ax_default label error transition notrs">
            <div id="u58_div" class="error"></div>
            <div id="u58_text" class="text ">
              <p><span>程序注释</span></p>
            </div>
          </div>

          <!-- Unnamed (Text field) -->
          <div id="u59" class="ax_default text_field error transition notrs">
            <div id="u59_div" class="error"></div>
            <input id="u59_input" type="text" value="" class="u59_input"/>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u60" class="ax_default label error transition notrs">
            <div id="u60_div" class="error"></div>
            <div id="u60_text" class="text ">
              <p><span>梯图行数</span></p>
            </div>
          </div>

          <!-- Unnamed (Text field) -->
          <div id="u61" class="ax_default text_field error transition notrs">
            <div id="u61_div" class="error"></div>
            <input id="u61_input" type="text" value="2975" class="u61_input"/>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u62" class="ax_default label error transition notrs">
            <div id="u62_div" class="error"></div>
            <div id="u62_text" class="text ">
              <p><span>梯图步数</span></p>
            </div>
          </div>

          <!-- Unnamed (Text field) -->
          <div id="u63" class="ax_default text_field error transition notrs">
            <div id="u63_div" class="error"></div>
            <input id="u63_input" type="text" value="4768" class="u63_input"/>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u64" class="ax_default label error transition notrs">
            <div id="u64_div" class="error"></div>
            <div id="u64_text" class="text ">
              <p><span>子程序数</span></p>
            </div>
          </div>

          <!-- Unnamed (Text field) -->
          <div id="u65" class="ax_default text_field error transition notrs">
            <div id="u65_div" class="error"></div>
            <input id="u65_input" type="text" value="17" class="u65_input"/>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u66" class="ax_default label error transition notrs">
            <div id="u66_div" class="error"></div>
            <div id="u66_text" class="text ">
              <p><span>PLC运行状态</span></p>
            </div>
          </div>

          <!-- Unnamed (Text field) -->
          <div id="u67" class="ax_default text_field error transition notrs">
            <div id="u67_div" class="error"></div>
            <input id="u67_input" type="text" value="运行" class="u67_input"/>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u68" class="ax_default label error transition notrs">
            <div id="u68_div" class="error"></div>
            <div id="u68_text" class="text ">
              <p><span>PLC1循环周期</span></p>
            </div>
          </div>

          <!-- Unnamed (Text field) -->
          <div id="u69" class="ax_default text_field error transition notrs">
            <div id="u69_div" class="error"></div>
            <input id="u69_input" type="text" value="1 ms" class="u69_input"/>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u70" class="ax_default label error transition notrs">
            <div id="u70_div" class="error"></div>
            <div id="u70_text" class="text ">
              <p><span>PLC2最大周期</span></p>
            </div>
          </div>

          <!-- Unnamed (Text field) -->
          <div id="u71" class="ax_default text_field error transition notrs">
            <div id="u71_div" class="error"></div>
            <input id="u71_input" type="text" value="8 ms" class="u71_input"/>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
