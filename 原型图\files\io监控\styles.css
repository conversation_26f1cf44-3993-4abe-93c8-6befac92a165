﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-0px;
  width:1024px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u182 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u183_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:767px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u183 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:1px;
  width:1024px;
  height:767px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u183 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u183_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u184 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u185_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u185 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u185 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u185_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u186 {
  border-width:0px;
  position:absolute;
  left:113px;
  top:11px;
  width:30px;
  height:30px;
  display:flex;
  transition:none;
}
#u186 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u186_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u186_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u187_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:30px;
  text-align:center;
}
#u187 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:30px;
  text-align:center;
}
#u187 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u187_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u188 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u189_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u189 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:702px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u189 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u189_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u190_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u190 {
  border-width:0px;
  position:absolute;
  left:132px;
  top:702px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u190 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u190_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u191_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u191 {
  border-width:0px;
  position:absolute;
  left:227px;
  top:702px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u191 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u191_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u192_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u192 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:702px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u192 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u192_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u193_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u193 {
  border-width:0px;
  position:absolute;
  left:417px;
  top:702px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u193 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u193_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u194_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u194 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:702px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u194 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u194_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u195_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u195 {
  border-width:0px;
  position:absolute;
  left:607px;
  top:702px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u195 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u195_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u196_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u196 {
  border-width:0px;
  position:absolute;
  left:702px;
  top:702px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u196 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u196_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u197_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u197 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:702px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u197 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u197_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u198_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u198 {
  border-width:0px;
  position:absolute;
  left:892px;
  top:702px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u198 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u198_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u199_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u199 {
  border-width:0px;
  position:absolute;
  left:987px;
  top:702px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u199 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u199_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u200_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u200 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:702px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u200 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u200_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u201 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:25px;
  width:57px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-62.19271366293822deg);
  -moz-transform:rotate(-62.19271366293822deg);
  -ms-transform:rotate(-62.19271366293822deg);
  transform:rotate(-62.19271366293822deg);
  transition:none;
}
#u201 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u201_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:58px;
  height:2px;
}
#u201_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u202 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u203_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:990px;
  height:620px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.9921568627450981);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  filter:drop-shadow(none);
  transition:none;
  color:transparent;
}
#u203 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:66px;
  width:990px;
  height:620px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:transparent;
}
#u203 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u203_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u204 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:96px;
  width:990px;
  height:1px;
  display:flex;
  transition:none;
}
#u204 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u204_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:991px;
  height:2px;
}
#u204_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u205_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:18px;
  text-align:center;
}
#u205 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:72px;
  width:56px;
  height:21px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:18px;
  text-align:center;
}
#u205 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u205_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
.u207_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u207 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:0px;
  width:50px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u207 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u207_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u208_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u208 {
  border-width:0px;
  position:absolute;
  left:119px;
  top:0px;
  width:50px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u208 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u208_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u209_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u209 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u209 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u209_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u210_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u210 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:0px;
  width:50px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u210 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u210_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u211_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u211 {
  border-width:0px;
  position:absolute;
  left:269px;
  top:0px;
  width:50px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u211 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u211_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u212_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u212 {
  border-width:0px;
  position:absolute;
  left:319px;
  top:0px;
  width:50px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u212 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u212_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u213_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u213 {
  border-width:0px;
  position:absolute;
  left:369px;
  top:0px;
  width:50px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u213 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u213_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u214_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u214 {
  border-width:0px;
  position:absolute;
  left:419px;
  top:0px;
  width:50px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u214 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u214_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u215_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u215 {
  border-width:0px;
  position:absolute;
  left:469px;
  top:0px;
  width:50px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u215 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u215_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u206 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:97px;
  width:519px;
  height:270px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border-radius:0px;
}
