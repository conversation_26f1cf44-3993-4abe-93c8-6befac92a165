﻿$axure.loadDocument(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,_(c,d,e,f,g,d,h,d,i,d,j,k,l,d,m,f,n,f,o,d,p,f),q,_(r,[_(s,t,u,v,w,x,y,z),_(s,A,u,B,w,x,y,C),_(s,D,u,E,w,x,y,F),_(s,G,u,H,w,x,y,I),_(s,J,u,K,w,x,y,L),_(s,M,u,N,w,x,y,O),_(s,P,u,Q,w,x,y,R),_(s,S,u,T,w,x,y,U),_(s,V,u,W,w,x,y,X),_(s,Y,u,Z,w,x,y,ba)]),bb,[bc,bd,be,bf],bg,[bh,bi,bj],bk,_(bl,bm),bn,_(bo,_(s,bp,bq,br,bs,bt,bu,bv,bw,bx,by,_(bz,bA,bB,bC),bD,bE,bF,f,bG,bH,bI,bv,bJ,bv,bK,bL,bM,f,bN,_(bO,bP,bQ,bP),bR,_(bS,bP,bT,bP),bU,bV,bW,d,bX,f,bY,bp,bZ,_(bz,bA,bB,ca),cb,_(bz,bA,bB,cc),cd,ce,cf,bA,cg,[ch],ci,ce,cj,ck,cl,cm,cn,cm,co,cp,cq,cr,cs,cr,ct,cr,cu,cr,cv,_(),cw,null,cx,null,cy,ck,cz,_(cA,f,cB,cC,cD,cC,cE,cC,cF,bP,bB,_(cG,ch,cH,ch,cI,ch,cJ,cK)),cL,_(cA,f,cB,bP,cD,cC,cE,cC,cF,bP,bB,_(cG,ch,cH,ch,cI,ch,cJ,cK)),cM,_(cA,f,cB,cN,cD,cN,cE,cC,cF,bP,bB,_(cG,ch,cH,ch,cI,ch,cJ,cO)),cP,_(cA,f,cQ,cR),cS,_(cA,f,cQ,cR),cT,cU,cV,_(cW,ch,cX,bP,cY,bL),cZ,_(da,cN,db,cN,dc,bP,dd,bP,de,bP),df,_(bO,dg,bQ,dg)),dh,_(di,_(s,dj),dk,_(s,dl),cw,_(s,dm,cd,ck),dn,_(s,dp,bZ,_(bz,bA,bB,dq)),dr,_(s,ds,cj,bx),dt,_(s,du,bD,dv,bs,dw,cd,ck,bZ,_(bz,bA,bB,dx,ci,bP),bG,dy,co,dz,cq,ck,cs,ck,ct,ck,cu,ck),dA,_(s,dB,bD,dC,bs,dw,cd,ck,bZ,_(bz,bA,bB,dx,ci,bP),bG,dy,co,dz,cq,ck,cs,ck,ct,ck,cu,ck),dD,_(s,dE,bD,dF,bs,dw,cd,ck,bZ,_(bz,bA,bB,dx,ci,bP),bG,dy,co,dz,cq,ck,cs,ck,ct,ck,cu,ck),dG,_(s,dH,bD,dI,bs,dw,cd,ck,bZ,_(bz,bA,bB,dx,ci,bP),bG,dy,co,dz,cq,ck,cs,ck,ct,ck,cu,ck),dJ,_(s,dK,bs,dw,cd,ck,bZ,_(bz,bA,bB,dx,ci,bP),bG,dy,co,dz,cq,ck,cs,ck,ct,ck,cu,ck),dL,_(s,dM,bD,dN,bs,dw,cd,ck,bZ,_(bz,bA,bB,dx,ci,bP),bG,dy,co,dz,cq,ck,cs,ck,ct,ck,cu,ck),dO,_(s,dP,bD,bE,cd,ck,bZ,_(bz,bA,bB,dx,ci,bP),bG,dy,co,dz,cq,ck,cs,ck,ct,ck,cu,ck),dQ,_(s,dR,cd,ck,bZ,_(bz,bA,bB,dx,ci,bP),bG,dy,co,dz,cq,ck,cs,ck,ct,ck,cu,ck),dS,_(s,dT,bZ,_(bz,bA,bB,dx,ci,bP)),dU,_(s,dV,by,_(bz,bA,bB,dW),bG,dy,co,cp),dX,_(s,dY,bZ,_(bz,dZ,ea,_(bO,eb,bQ,bP),ec,_(bO,eb,bQ,cN),ed,[_(bB,ca,ee,bP,ci,cN),_(bB,dq,ee,bP,ci,cN),_(bB,ef,ee,cN,ci,cN),_(bB,ca,ee,cN,ci,cN)])),eg,_(s,eh,cd,ck,bZ,_(bz,bA,bB,ei),cz,_(cA,d,cB,cC,cD,cC,cE,cC,cF,bP,bB,_(cG,ch,cH,ch,cI,ch,cJ,ej)),bG,dy,co,dz,cq,ek,cs,ek,ct,ek,cu,ek),el,_(s,em,by,_(bz,bA,bB,en)),eo,_(s,ep,bZ,_(bz,bA,bB,eq))),er,_()));}; 
var b="configuration",c="showPageNotes",d=true,e="showPageNoteNames",f=false,g="showAnnotations",h="showAnnotationsSidebar",i="showConsole",j="linkStyle",k="displayMultipleTargetsOnly",l="linkFlowsToPages",m="linkFlowsToPagesNewWindow",n="useLabels",o="useViews",p="loadFeedbackPlugin",q="sitemap",r="rootNodes",s="id",t="yh7hy1",u="pageName",v="一级菜单",w="type",x="Wireframe",y="url",z="一级菜单.html",A="5iqniz",B="二级菜单",C="二级菜单.html",D="tpfqnf",E="三级菜单",F="三级菜单.html",G="ja0vq9",H="梯图信息",I="梯图信息.html",J="dwrkbg",K="梯图编辑1",L="梯图编辑1.html",M="uu7gdk",N="梯图编辑2",O="梯图编辑2.html",P="yjli8a",Q="符号表",R="符号表.html",S="b8bf09",T="IO对照表",U="io对照表.html",V="7qjflp",W="IO监控",X="io监控.html",Y="so91a2",Z="素材",ba="素材.html",bb="additionalJs",bc="plugins/debug/debug.js",bd="plugins/sitemap/sitemap.js",be="plugins/page_notes/page_notes.js",bf="resources/scripts/hintmanager.js",bg="additionalCss",bh="plugins/debug/styles/debug.css",bi="plugins/sitemap/styles/sitemap.css",bj="plugins/page_notes/styles/page_notes.css",bk="globalVariables",bl="onloadvariable",bm="",bn="stylesheet",bo="defaultStyle",bp="627587b6038d43cca051c114ac41ad32",bq="fontName",br="\"Arial Normal\", \"Arial\", sans-serif",bs="fontWeight",bt="400",bu="fontStyle",bv="normal",bw="fontStretch",bx="5",by="foreGroundFill",bz="fillType",bA="solid",bB="color",bC=0xFF333333,bD="fontSize",bE="13px",bF="underline",bG="horizontalAlignment",bH="center",bI="lineSpacing",bJ="characterSpacing",bK="letterCase",bL="none",bM="strikethrough",bN="location",bO="x",bP=0,bQ="y",bR="size",bS="width",bT="height",bU="buttonSize",bV="12",bW="visible",bX="limbo",bY="baseStyle",bZ="fill",ca=0xFFFFFFFF,cb="borderFill",cc=0xFF797979,cd="borderWidth",ce="1",cf="linePattern",cg="linePatternArray",ch=0,ci="opacity",cj="cornerRadius",ck="0",cl="borderVisibility",cm="top right bottom left",cn="cornerVisibility",co="verticalAlignment",cp="middle",cq="paddingLeft",cr="2",cs="paddingTop",ct="paddingRight",cu="paddingBottom",cv="stateStyles",cw="image",cx="imageFilter",cy="rotation",cz="outerShadow",cA="on",cB="offsetX",cC=5,cD="offsetY",cE="blurRadius",cF="spread",cG="r",cH="g",cI="b",cJ="a",cK=0.34901960784313724,cL="innerShadow",cM="textShadow",cN=1,cO=0.6470588235294118,cP="widgetBlur",cQ="radius",cR=4,cS="backdropBlur",cT="viewOverride",cU="19e82109f102476f933582835c373474",cV="transition",cW="easing",cX="duration",cY="css",cZ="transform",da="scaleX",db="scaleY",dc="translateX",dd="translateY",de="rotate",df="transformOrigin",dg=50,dh="customStyles",di="box_1",dj="********************************",dk="shape",dl="40519e9ec4264601bfb12c514e4f4867",dm="75a91ee5b9d042cfa01b8d565fe289c0",dn="placeholder",dp="c50e74f669b24b37bd9c18da7326bccd",dq=0xFFF2F2F2,dr="button",ds="c9f35713a1cf4e91a0f2dbac65e6fb5c",dt="heading_1",du="1111111151944dfba49f67fd55eb1f88",dv="32px",dw="bold",dx=0xFFFFFF,dy="left",dz="top",dA="heading_2",dB="b3a15c9ddde04520be40f94c8168891e",dC="24px",dD="heading_3",dE="8c7a4c5ad69a4369a5f7788171ac0b32",dF="18px",dG="heading_4",dH="e995c891077945c89c0b5fe110d15a0b",dI="14px",dJ="heading_5",dK="386b19ef4be143bd9b6c392ded969f89",dL="heading_6",dM="fc3b9a13b5574fa098ef0a1db9aac861",dN="10px",dO="label",dP="2285372321d148ec80932747449c36c9",dQ="paragraph",dR="4988d43d80b44008a4a415096f1632af",dS="line",dT="619b2148ccc1497285562264d51992f9",dU="text_field",dV="44157808f2934100b68f2394a66b2bba",dW=0xFF000000,dX="flow_shape",dY="df01900e3c4e43f284bafec04b0864c4",dZ="linearGradient",ea="startPoint",eb=0.5,ec="endPoint",ed="stops",ee="offset",ef=0xFFE4E4E4,eg="sticky_1",eh="31e8887730cc439f871dc77ac74c53b6",ei=0xFFFFDF25,ej=0.2,ek="10",el="form_hint",em="4889d666e8ad4c5e81e59863039a5cc0",en=0xFF999999,eo="form_disabled",ep="9bd0236217a94d89b0314c8c7fc75f16",eq=0xFFF0F0F0,er="duplicateStyles";
return _creator();
})());