﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-0px;
  width:1024px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u124 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u125_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:768px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u125 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:1px;
  width:1024px;
  height:768px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u125 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u125_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u126 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u127_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u127 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u127 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u127_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u128 {
  border-width:0px;
  position:absolute;
  left:113px;
  top:11px;
  width:30px;
  height:30px;
  display:flex;
  transition:none;
}
#u128 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u128_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u128_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u129_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:30px;
  text-align:center;
}
#u129 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:30px;
  text-align:center;
}
#u129 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u129_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u130 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u131_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u131 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:703px;
  width:1024px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u131 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u131_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u132_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u132 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u132 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u132_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u133_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u133 {
  border-width:0px;
  position:absolute;
  left:132px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u133 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u133_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u134_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u134 {
  border-width:0px;
  position:absolute;
  left:227px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u134 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u134_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u135_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u135 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u135 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u135_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u136_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u136 {
  border-width:0px;
  position:absolute;
  left:417px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u136 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u136_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u137_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u137 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u137 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u137_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u138_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u138 {
  border-width:0px;
  position:absolute;
  left:607px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u138 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u138_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u139_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u139 {
  border-width:0px;
  position:absolute;
  left:702px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u139 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u139_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u140_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u140 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u140 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u140_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u141_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u141 {
  border-width:0px;
  position:absolute;
  left:892px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u141 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u141_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u142_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u142 {
  border-width:0px;
  position:absolute;
  left:987px;
  top:703px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u142 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u142_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u143_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u143 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:703px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u143 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u143_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u144 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:25px;
  width:57px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-62.19271366293822deg);
  -moz-transform:rotate(-62.19271366293822deg);
  -ms-transform:rotate(-62.19271366293822deg);
  transform:rotate(-62.19271366293822deg);
  transition:none;
}
#u144 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u144_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:58px;
  height:2px;
}
#u144_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u145 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u146_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:990px;
  height:621px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.9921568627450981);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  filter:drop-shadow(none);
  transition:none;
  color:transparent;
}
#u146 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:66px;
  width:990px;
  height:621px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:transparent;
}
#u146 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u146_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u147 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:96px;
  width:990px;
  height:1px;
  display:flex;
  transition:none;
}
#u147 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u147_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:991px;
  height:2px;
}
#u147_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u148_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:18px;
  text-align:center;
}
#u148 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:72px;
  width:54px;
  height:21px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:18px;
  text-align:center;
}
#u148 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u148_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
.u150_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u150 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:0px;
  width:200px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u150 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u150_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u151_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u151 {
  border-width:0px;
  position:absolute;
  left:150px;
  top:0px;
  width:150px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u151 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u151_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u152_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u152 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u152 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u152_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u149 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:98px;
  width:500px;
  height:120px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border-radius:0px;
}
