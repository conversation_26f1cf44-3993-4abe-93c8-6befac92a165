﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-0px;
  width:2173px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u216 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:768px;
  display:flex;
  transition:none;
}
#u216 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u216_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:768px;
}
#u216_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u217 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u218_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:768px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u218 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:928px;
  width:1024px;
  height:768px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u218 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u218_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u219 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u220_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u220 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:927px;
  width:1024px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u220 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u220_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u221 {
  border-width:0px;
  position:absolute;
  left:113px;
  top:938px;
  width:30px;
  height:30px;
  display:flex;
  transition:none;
}
#u221 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u221_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u221_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u222_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:30px;
  text-align:center;
}
#u222 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:927px;
  width:96px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:30px;
  text-align:center;
}
#u222 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u222_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u223 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u224_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u224 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:1630px;
  width:1024px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u224 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u224_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u225_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u225 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:1630px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u225 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u225_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u226_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u226 {
  border-width:0px;
  position:absolute;
  left:132px;
  top:1630px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u226 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u226_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u227_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u227 {
  border-width:0px;
  position:absolute;
  left:227px;
  top:1630px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u227 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u227_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u228_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u228 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:1630px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u228 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u228_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u229_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u229 {
  border-width:0px;
  position:absolute;
  left:417px;
  top:1630px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u229 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u229_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u230_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u230 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:1630px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u230 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u230_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u231_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u231 {
  border-width:0px;
  position:absolute;
  left:607px;
  top:1630px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u231 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u231_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u232_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u232 {
  border-width:0px;
  position:absolute;
  left:702px;
  top:1630px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u232 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u232_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u233_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u233 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:1630px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u233 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u233_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u234_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u234 {
  border-width:0px;
  position:absolute;
  left:892px;
  top:1630px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u234 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u234_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u235_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u235 {
  border-width:0px;
  position:absolute;
  left:987px;
  top:1630px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u235 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u235_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u236_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u236 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:1630px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u236 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u236_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u237 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:952px;
  width:57px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-62.19271366293822deg);
  -moz-transform:rotate(-62.19271366293822deg);
  -ms-transform:rotate(-62.19271366293822deg);
  transform:rotate(-62.19271366293822deg);
  transition:none;
}
#u237 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u237_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:58px;
  height:2px;
}
#u237_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u238 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u239_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:990px;
  height:621px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.9921568627450981);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  filter:drop-shadow(none);
  transition:none;
  color:transparent;
}
#u239 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:992px;
  width:990px;
  height:621px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:transparent;
}
#u239 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u239_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u240 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:1022px;
  width:990px;
  height:1px;
  display:flex;
  transition:none;
}
#u240 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u240_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:991px;
  height:2px;
}
#u240_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u241_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:18px;
  text-align:center;
}
#u241 {
  border-width:0px;
  position:absolute;
  left:58px;
  top:998px;
  width:1px;
  height:21px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:18px;
  text-align:center;
}
#u241 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u241_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u242 {
  border-width:0px;
  position:absolute;
  left:1149px;
  top:0px;
  width:1024px;
  height:768px;
  display:flex;
  transition:none;
}
#u242 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u242_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:768px;
}
#u242_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u243 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u244 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u245_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u245 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:1756px;
  width:1024px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u245 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u245_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u246 {
  border-width:0px;
  position:absolute;
  left:113px;
  top:1767px;
  width:30px;
  height:30px;
  display:flex;
  transition:none;
}
#u246 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u246_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u246_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u247_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:30px;
  text-align:center;
}
#u247 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:1756px;
  width:96px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:30px;
  text-align:center;
}
#u247 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u247_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u248 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u249_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:990px;
  height:621px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.9921568627450981);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  filter:drop-shadow(none);
  transition:none;
  color:transparent;
}
#u249 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:1822px;
  width:990px;
  height:621px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:transparent;
}
#u249 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u249_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u250 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:1852px;
  width:990px;
  height:1px;
  display:flex;
  transition:none;
}
#u250 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u250_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:991px;
  height:2px;
}
#u250_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u251_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:18px;
  text-align:center;
}
#u251 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:1828px;
  width:72px;
  height:21px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:18px;
  text-align:center;
}
#u251 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u251_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u252 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u253_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:65px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u253 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2459px;
  width:1024px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u253 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u253_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u254_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u254 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:2459px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u254 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u254_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u255_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u255 {
  border-width:0px;
  position:absolute;
  left:132px;
  top:2459px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u255 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u255_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u256_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u256 {
  border-width:0px;
  position:absolute;
  left:227px;
  top:2459px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u256 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u256_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u257_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u257 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:2459px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u257 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u257_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u258_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u258 {
  border-width:0px;
  position:absolute;
  left:417px;
  top:2459px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u258 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u258_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u259_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u259 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:2459px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u259 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u259_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u260_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u260 {
  border-width:0px;
  position:absolute;
  left:607px;
  top:2459px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u260 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u260_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u261_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u261 {
  border-width:0px;
  position:absolute;
  left:702px;
  top:2459px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u261 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u261_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u262_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u262 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:2459px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u262 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u262_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u263_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u263 {
  border-width:0px;
  position:absolute;
  left:892px;
  top:2459px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u263 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u263_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u264_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u264 {
  border-width:0px;
  position:absolute;
  left:987px;
  top:2459px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u264 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u264_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u265_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u265 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2459px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u265 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u265_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u266 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:1781px;
  width:57px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-62.19271366293822deg);
  -moz-transform:rotate(-62.19271366293822deg);
  -ms-transform:rotate(-62.19271366293822deg);
  transform:rotate(-62.19271366293822deg);
  transition:none;
}
#u266 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u266_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:58px;
  height:2px;
}
#u266_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u267_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:16px;
}
#u267 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:1888px;
  width:32px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:16px;
}
#u267 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u267_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u268_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u268_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u268_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u268_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u268_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u268 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:1884px;
  width:300px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u268 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u268_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u268.hint {
}
#u268_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u268.disabled {
}
#u268_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u268.hint.disabled {
}
#u269_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:16px;
}
#u269 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:1926px;
  width:32px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:16px;
}
#u269 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u269_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u270_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u270_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u270_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u270_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u270_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u270 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:1922px;
  width:300px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u270 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u270_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u270.hint {
}
#u270_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u270.disabled {
}
#u270_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u270.hint.disabled {
}
#u271_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:16px;
}
#u271 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:1963px;
  width:32px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:16px;
}
#u271 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u271_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u272_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u272_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u272_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u272_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u272_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u272 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:1959px;
  width:300px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u272 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u272_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u272.hint {
}
#u272_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u272.disabled {
}
#u272_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u272.hint.disabled {
}
#u273_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:16px;
}
#u273 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:2115px;
  width:64px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:16px;
}
#u273 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u273_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u274_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:65px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u274_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:65px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u274_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:65px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u274_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:65px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u274_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:65px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u274 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:2111px;
  width:300px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u274 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u274_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:65px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u274.hint {
}
#u274_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:65px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u274.disabled {
}
#u274_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:65px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u274.hint.disabled {
}
#u275_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:16px;
}
#u275 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:2001px;
  width:64px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:16px;
}
#u275 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u275_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u276_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u276_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u276_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u276_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u276_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u276 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:1997px;
  width:300px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u276 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u276_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u276.hint {
}
#u276_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u276.disabled {
}
#u276_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u276.hint.disabled {
}
#u277_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:16px;
}
#u277 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:2039px;
  width:64px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:16px;
}
#u277 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u277_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u278_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u278_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u278_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u278_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u278_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u278 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:2035px;
  width:300px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u278 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u278_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u278.hint {
}
#u278_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u278.disabled {
}
#u278_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u278.hint.disabled {
}
#u279_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:16px;
}
#u279 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:2077px;
  width:64px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:16px;
}
#u279 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u279_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u280_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u280_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u280_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u280_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u280_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u280 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:2073px;
  width:300px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u280 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u280_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u280.hint {
}
#u280_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u280.disabled {
}
#u280_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u280.hint.disabled {
}
#u281_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:16px;
}
#u281 {
  border-width:0px;
  position:absolute;
  left:544px;
  top:1888px;
  width:96px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:16px;
}
#u281 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u281_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u282_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u282_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u282_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u282_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u282_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u282 {
  border-width:0px;
  position:absolute;
  left:655px;
  top:1884px;
  width:300px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u282 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u282_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u282.hint {
}
#u282_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u282.disabled {
}
#u282_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u282.hint.disabled {
}
#u283_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:16px;
}
#u283 {
  border-width:0px;
  position:absolute;
  left:544px;
  top:1926px;
  width:105px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:16px;
}
#u283 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u283_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u284_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u284_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u284_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u284_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u284_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u284 {
  border-width:0px;
  position:absolute;
  left:655px;
  top:1922px;
  width:300px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u284 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u284_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u284.hint {
}
#u284_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u284.disabled {
}
#u284_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u284.hint.disabled {
}
#u285_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:16px;
}
#u285 {
  border-width:0px;
  position:absolute;
  left:544px;
  top:1963px;
  width:105px;
  height:18px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:16px;
}
#u285 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u285_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u286_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u286_input.hint {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u286_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u286_input.hint.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  padding:2px 2px 2px 2px;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#999999;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u286_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u286 {
  border-width:0px;
  position:absolute;
  left:655px;
  top:1959px;
  width:300px;
  height:25px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u286 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u286_div.hint {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u286.hint {
}
#u286_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u286.disabled {
}
#u286_div.hint.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u286.hint.disabled {
}
#u287_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:768px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u287 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:1756px;
  width:1024px;
  height:768px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u287 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u287_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u288 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u289 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u290_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u290 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2615px;
  width:1024px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u290 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u290_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u291 {
  border-width:0px;
  position:absolute;
  left:113px;
  top:2626px;
  width:30px;
  height:30px;
  display:flex;
  transition:none;
}
#u291 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u291_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u291_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u292_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:30px;
  text-align:center;
}
#u292 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2615px;
  width:96px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:30px;
  text-align:center;
}
#u292 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u292_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u293 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:2640px;
  width:57px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-62.19271366293822deg);
  -moz-transform:rotate(-62.19271366293822deg);
  -ms-transform:rotate(-62.19271366293822deg);
  transform:rotate(-62.19271366293822deg);
  transition:none;
}
#u293 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u293_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:58px;
  height:2px;
}
#u293_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u294 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u295_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u295 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:3318px;
  width:1024px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u295 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u295_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u296_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u296 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:3318px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u296 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u296_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u297_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u297 {
  border-width:0px;
  position:absolute;
  left:132px;
  top:3318px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u297 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u297_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u298_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u298 {
  border-width:0px;
  position:absolute;
  left:227px;
  top:3318px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u298 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u298_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u299_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u299 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:3318px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u299 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u299_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u300_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u300 {
  border-width:0px;
  position:absolute;
  left:417px;
  top:3318px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u300 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u300_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u301_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u301 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:3318px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u301 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u301_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u302_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u302 {
  border-width:0px;
  position:absolute;
  left:607px;
  top:3318px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u302 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u302_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u303_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u303 {
  border-width:0px;
  position:absolute;
  left:702px;
  top:3318px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u303 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u303_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u304_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u304 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:3318px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u304 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u304_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u305_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u305 {
  border-width:0px;
  position:absolute;
  left:892px;
  top:3318px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u305 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u305_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u306_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u306 {
  border-width:0px;
  position:absolute;
  left:987px;
  top:3318px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u306 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u306_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u307_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u307 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:3318px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u307 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u307_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u308 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u309_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:990px;
  height:621px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.9921568627450981);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  filter:drop-shadow(none);
  transition:none;
  color:transparent;
}
#u309 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:2681px;
  width:990px;
  height:621px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:transparent;
}
#u309 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u309_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u310 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:2711px;
  width:990px;
  height:1px;
  display:flex;
  transition:none;
}
#u310 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u310_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:991px;
  height:2px;
}
#u310_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u311_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:18px;
  text-align:center;
}
#u311 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:2687px;
  width:72px;
  height:21px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:18px;
  text-align:center;
}
#u311 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u311_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u312 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:2711px;
  width:851px;
  height:591px;
  display:flex;
  transition:none;
}
#u312 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u312_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:851px;
  height:591px;
}
#u312_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u313 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u314_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:768px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u314 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:3443px;
  width:1024px;
  height:768px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u314 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u314_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u315 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u316_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u316 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:3442px;
  width:1024px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u316 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u316_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u317 {
  border-width:0px;
  position:absolute;
  left:113px;
  top:3453px;
  width:30px;
  height:30px;
  display:flex;
  transition:none;
}
#u317 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u317_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u317_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u318_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:30px;
  text-align:center;
}
#u318 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:3442px;
  width:96px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:30px;
  text-align:center;
}
#u318 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u318_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u319 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u320_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u320 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:4145px;
  width:1024px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u320 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u320_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u321_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u321 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:4145px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u321 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u321_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u322_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u322 {
  border-width:0px;
  position:absolute;
  left:132px;
  top:4145px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u322 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u322_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u323_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u323 {
  border-width:0px;
  position:absolute;
  left:227px;
  top:4145px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u323 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u323_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u324_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u324 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:4145px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u324 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u324_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u325_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u325 {
  border-width:0px;
  position:absolute;
  left:417px;
  top:4145px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u325 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u325_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u326_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u326 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:4145px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u326 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u326_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u327_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u327 {
  border-width:0px;
  position:absolute;
  left:607px;
  top:4145px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u327 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u327_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u328_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u328 {
  border-width:0px;
  position:absolute;
  left:702px;
  top:4145px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u328 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u328_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u329_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u329 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:4145px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u329 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u329_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u330_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u330 {
  border-width:0px;
  position:absolute;
  left:892px;
  top:4145px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u330 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u330_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u331_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u331 {
  border-width:0px;
  position:absolute;
  left:987px;
  top:4145px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u331 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u331_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u332_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u332 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:4145px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u332 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u332_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u333 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:3467px;
  width:57px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-62.19271366293822deg);
  -moz-transform:rotate(-62.19271366293822deg);
  -ms-transform:rotate(-62.19271366293822deg);
  transform:rotate(-62.19271366293822deg);
  transition:none;
}
#u333 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u333_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:58px;
  height:2px;
}
#u333_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u334 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u335_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:990px;
  height:621px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.9921568627450981);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  filter:drop-shadow(none);
  transition:none;
  color:transparent;
}
#u335 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:3508px;
  width:990px;
  height:621px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:transparent;
}
#u335 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u335_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u336 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:3538px;
  width:990px;
  height:1px;
  display:flex;
  transition:none;
}
#u336 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u336_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:991px;
  height:2px;
}
#u336_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u337_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:18px;
  text-align:center;
}
#u337 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:3514px;
  width:72px;
  height:21px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:18px;
  text-align:center;
}
#u337 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u337_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u338 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:3538px;
  width:851px;
  height:591px;
  display:flex;
  transition:none;
}
#u338 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u338_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:851px;
  height:591px;
}
#u338_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u339 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u340_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:768px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u340 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:4274px;
  width:1024px;
  height:768px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u340 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u340_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u341 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u342_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u342 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:4273px;
  width:1024px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u342 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u342_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u343 {
  border-width:0px;
  position:absolute;
  left:113px;
  top:4284px;
  width:30px;
  height:30px;
  display:flex;
  transition:none;
}
#u343 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u343_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u343_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u344_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:30px;
  text-align:center;
}
#u344 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:4273px;
  width:96px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:30px;
  text-align:center;
}
#u344 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u344_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u345 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u346_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u346 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:4976px;
  width:1024px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u346 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u346_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u347_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u347 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:4976px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u347 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u347_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u348_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u348 {
  border-width:0px;
  position:absolute;
  left:132px;
  top:4976px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u348 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u348_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u349_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u349 {
  border-width:0px;
  position:absolute;
  left:227px;
  top:4976px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u349 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u349_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u350_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u350 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:4976px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u350 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u350_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u351_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u351 {
  border-width:0px;
  position:absolute;
  left:417px;
  top:4976px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u351 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u351_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u352_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u352 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:4976px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u352 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u352_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u353_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u353 {
  border-width:0px;
  position:absolute;
  left:607px;
  top:4976px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u353 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u353_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u354_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u354 {
  border-width:0px;
  position:absolute;
  left:702px;
  top:4976px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u354 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u354_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u355_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u355 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:4976px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u355 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u355_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u356_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u356 {
  border-width:0px;
  position:absolute;
  left:892px;
  top:4976px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u356 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u356_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u357_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u357 {
  border-width:0px;
  position:absolute;
  left:987px;
  top:4976px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u357 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u357_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u358_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u358 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:4976px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u358 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u358_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u359 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:4298px;
  width:57px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-62.19271366293822deg);
  -moz-transform:rotate(-62.19271366293822deg);
  -ms-transform:rotate(-62.19271366293822deg);
  transform:rotate(-62.19271366293822deg);
  transition:none;
}
#u359 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u359_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:58px;
  height:2px;
}
#u359_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u360 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u361_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:990px;
  height:621px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.9921568627450981);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  filter:drop-shadow(none);
  transition:none;
  color:transparent;
}
#u361 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:4339px;
  width:990px;
  height:621px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:transparent;
}
#u361 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u361_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u362 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:4369px;
  width:990px;
  height:1px;
  display:flex;
  transition:none;
}
#u362 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u362_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:991px;
  height:2px;
}
#u362_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u363_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:18px;
  text-align:center;
}
#u363 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:4345px;
  width:54px;
  height:21px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:18px;
  text-align:center;
}
#u363 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u363_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
.u365_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u365 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:0px;
  width:200px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u365 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u365_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u366_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u366 {
  border-width:0px;
  position:absolute;
  left:150px;
  top:0px;
  width:150px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u366 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u366_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u367_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u367 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u367 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u367_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u364 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:4370px;
  width:500px;
  height:120px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border-radius:0px;
}
#u368 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u369_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:768px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u369 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:5101px;
  width:1024px;
  height:768px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u369 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u369_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u370 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u371_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u371 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:5100px;
  width:1024px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u371 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u371_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u372 {
  border-width:0px;
  position:absolute;
  left:113px;
  top:5111px;
  width:30px;
  height:30px;
  display:flex;
  transition:none;
}
#u372 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u372_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u372_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u373_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:30px;
  text-align:center;
}
#u373 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:5100px;
  width:96px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:30px;
  text-align:center;
}
#u373 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u373_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u374 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u375_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u375 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:5803px;
  width:1024px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u375 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u375_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u376_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u376 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:5803px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u376 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u376_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u377_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u377 {
  border-width:0px;
  position:absolute;
  left:132px;
  top:5803px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u377 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u377_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u378_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u378 {
  border-width:0px;
  position:absolute;
  left:227px;
  top:5803px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u378 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u378_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u379_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u379 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:5803px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u379 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u379_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u380_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u380 {
  border-width:0px;
  position:absolute;
  left:417px;
  top:5803px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u380 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u380_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u381_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u381 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:5803px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u381 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u381_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u382_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u382 {
  border-width:0px;
  position:absolute;
  left:607px;
  top:5803px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u382 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u382_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u383_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u383 {
  border-width:0px;
  position:absolute;
  left:702px;
  top:5803px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u383 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u383_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u384_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u384 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:5803px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u384 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u384_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u385_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u385 {
  border-width:0px;
  position:absolute;
  left:892px;
  top:5803px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u385 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u385_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u386_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u386 {
  border-width:0px;
  position:absolute;
  left:987px;
  top:5803px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u386 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u386_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u387_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u387 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:5803px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u387 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u387_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u388 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:5125px;
  width:57px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-62.19271366293822deg);
  -moz-transform:rotate(-62.19271366293822deg);
  -ms-transform:rotate(-62.19271366293822deg);
  transform:rotate(-62.19271366293822deg);
  transition:none;
}
#u388 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u388_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:58px;
  height:2px;
}
#u388_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u389 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u390_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:990px;
  height:621px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.9921568627450981);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  filter:drop-shadow(none);
  transition:none;
  color:transparent;
}
#u390 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:5166px;
  width:990px;
  height:621px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:transparent;
}
#u390 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u390_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u391 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:5196px;
  width:990px;
  height:1px;
  display:flex;
  transition:none;
}
#u391 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u391_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:991px;
  height:2px;
}
#u391_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u392_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:18px;
  text-align:center;
}
#u392 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:5172px;
  width:74px;
  height:21px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:18px;
  text-align:center;
}
#u392 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u392_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
.u394_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u394 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:0px;
  width:200px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u394 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u394_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u395_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u395 {
  border-width:0px;
  position:absolute;
  left:150px;
  top:0px;
  width:150px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u395 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u395_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u396_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u396 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u396 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u396_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u393 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:5198px;
  width:500px;
  height:150px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border-radius:0px;
}
#u397 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u398_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:767px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u398 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:5928px;
  width:1024px;
  height:767px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u398 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u398_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u399 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u400_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u400 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:5927px;
  width:1024px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u400 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u400_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u401 {
  border-width:0px;
  position:absolute;
  left:113px;
  top:5938px;
  width:30px;
  height:30px;
  display:flex;
  transition:none;
}
#u401 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u401_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u401_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u402_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:30px;
  text-align:center;
}
#u402 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:5927px;
  width:96px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:30px;
  text-align:center;
}
#u402 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u402_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u403 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u404_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u404 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:6629px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u404 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u404_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u405_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u405 {
  border-width:0px;
  position:absolute;
  left:132px;
  top:6629px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u405 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u405_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u406_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u406 {
  border-width:0px;
  position:absolute;
  left:227px;
  top:6629px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u406 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u406_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u407_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u407 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:6629px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u407 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u407_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u408_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u408 {
  border-width:0px;
  position:absolute;
  left:417px;
  top:6629px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u408 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u408_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u409_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u409 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:6629px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u409 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u409_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u410_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u410 {
  border-width:0px;
  position:absolute;
  left:607px;
  top:6629px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u410 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u410_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u411_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u411 {
  border-width:0px;
  position:absolute;
  left:702px;
  top:6629px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u411 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u411_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u412_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u412 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:6629px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u412 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u412_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u413_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u413 {
  border-width:0px;
  position:absolute;
  left:892px;
  top:6629px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u413 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u413_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u414_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u414 {
  border-width:0px;
  position:absolute;
  left:987px;
  top:6629px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u414 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u414_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u415_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u415 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:6629px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u415 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u415_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u416 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:5952px;
  width:57px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-62.19271366293822deg);
  -moz-transform:rotate(-62.19271366293822deg);
  -ms-transform:rotate(-62.19271366293822deg);
  transform:rotate(-62.19271366293822deg);
  transition:none;
}
#u416 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u416_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:58px;
  height:2px;
}
#u416_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u417 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u418_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:990px;
  height:620px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.9921568627450981);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  filter:drop-shadow(none);
  transition:none;
  color:transparent;
}
#u418 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:5993px;
  width:990px;
  height:620px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:transparent;
}
#u418 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u418_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u419 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:6023px;
  width:990px;
  height:1px;
  display:flex;
  transition:none;
}
#u419 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u419_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:991px;
  height:2px;
}
#u419_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u420_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:18px;
  text-align:center;
}
#u420 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:5999px;
  width:72px;
  height:21px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:18px;
  text-align:center;
}
#u420 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u420_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u421 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:6024px;
  width:850px;
  height:587px;
  display:flex;
  transition:none;
}
#u421 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u421_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:850px;
  height:587px;
}
#u421_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u422 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u423_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:767px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u423 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:6737px;
  width:1024px;
  height:767px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u423 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u423_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u424 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u425_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u425 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:6736px;
  width:1024px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u425 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u425_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u426 {
  border-width:0px;
  position:absolute;
  left:113px;
  top:6747px;
  width:30px;
  height:30px;
  display:flex;
  transition:none;
}
#u426 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u426_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u426_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u427_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:30px;
  text-align:center;
}
#u427 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:6736px;
  width:96px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:30px;
  text-align:center;
}
#u427 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u427_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u428 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u429_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u429 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:7438px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u429 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u429_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u430_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u430 {
  border-width:0px;
  position:absolute;
  left:132px;
  top:7438px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u430 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u430_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u431_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u431 {
  border-width:0px;
  position:absolute;
  left:227px;
  top:7438px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u431 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u431_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u432_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u432 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:7438px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u432 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u432_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u433_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u433 {
  border-width:0px;
  position:absolute;
  left:417px;
  top:7438px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u433 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u433_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u434_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u434 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:7438px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u434 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u434_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u435_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u435 {
  border-width:0px;
  position:absolute;
  left:607px;
  top:7438px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u435 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u435_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u436_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u436 {
  border-width:0px;
  position:absolute;
  left:702px;
  top:7438px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u436 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u436_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u437_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u437 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:7438px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u437 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u437_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u438_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u438 {
  border-width:0px;
  position:absolute;
  left:892px;
  top:7438px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u438 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u438_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u439_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u439 {
  border-width:0px;
  position:absolute;
  left:987px;
  top:7438px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u439 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u439_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u440_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u440 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7438px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u440 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u440_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u441 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:6761px;
  width:57px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-62.19271366293822deg);
  -moz-transform:rotate(-62.19271366293822deg);
  -ms-transform:rotate(-62.19271366293822deg);
  transform:rotate(-62.19271366293822deg);
  transition:none;
}
#u441 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u441_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:58px;
  height:2px;
}
#u441_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u442 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u443_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:990px;
  height:620px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.9921568627450981);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  filter:drop-shadow(none);
  transition:none;
  color:transparent;
}
#u443 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:6802px;
  width:990px;
  height:620px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:transparent;
}
#u443 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u443_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u444 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:6832px;
  width:990px;
  height:1px;
  display:flex;
  transition:none;
}
#u444 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u444_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:991px;
  height:2px;
}
#u444_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u445_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:18px;
  text-align:center;
}
#u445 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:6808px;
  width:72px;
  height:21px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:18px;
  text-align:center;
}
#u445 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u445_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u446 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:6833px;
  width:850px;
  height:587px;
  display:flex;
  transition:none;
}
#u446 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u446_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:850px;
  height:587px;
}
#u446_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u447 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u448_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:767px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u448 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7562px;
  width:1024px;
  height:767px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u448 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u448_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u449 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u450_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u450 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7561px;
  width:1024px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u450 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u450_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u451 {
  border-width:0px;
  position:absolute;
  left:113px;
  top:7572px;
  width:30px;
  height:30px;
  display:flex;
  transition:none;
}
#u451 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u451_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u451_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u452_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:30px;
  text-align:center;
}
#u452 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7561px;
  width:96px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:30px;
  text-align:center;
}
#u452 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u452_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u453 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u454_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u454 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:8263px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u454 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u454_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u455_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u455 {
  border-width:0px;
  position:absolute;
  left:132px;
  top:8263px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u455 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u455_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u456_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u456 {
  border-width:0px;
  position:absolute;
  left:227px;
  top:8263px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u456 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u456_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u457_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u457 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:8263px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u457 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u457_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u458_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u458 {
  border-width:0px;
  position:absolute;
  left:417px;
  top:8263px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u458 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u458_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u459_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u459 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:8263px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u459 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u459_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u460_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u460 {
  border-width:0px;
  position:absolute;
  left:607px;
  top:8263px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u460 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u460_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u461_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u461 {
  border-width:0px;
  position:absolute;
  left:702px;
  top:8263px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u461 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u461_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u462_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u462 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:8263px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u462 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u462_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u463_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u463 {
  border-width:0px;
  position:absolute;
  left:892px;
  top:8263px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u463 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u463_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u464_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u464 {
  border-width:0px;
  position:absolute;
  left:987px;
  top:8263px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u464 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u464_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u465_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u465 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:8263px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u465 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u465_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u466 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:7586px;
  width:57px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-62.19271366293822deg);
  -moz-transform:rotate(-62.19271366293822deg);
  -ms-transform:rotate(-62.19271366293822deg);
  transform:rotate(-62.19271366293822deg);
  transition:none;
}
#u466 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u466_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:58px;
  height:2px;
}
#u466_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u467 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u468_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:990px;
  height:620px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.9921568627450981);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  filter:drop-shadow(none);
  transition:none;
  color:transparent;
}
#u468 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:7627px;
  width:990px;
  height:620px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:transparent;
}
#u468 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u468_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u469 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:7657px;
  width:990px;
  height:1px;
  display:flex;
  transition:none;
}
#u469 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u469_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:991px;
  height:2px;
}
#u469_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u470_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:18px;
  text-align:center;
}
#u470 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:7633px;
  width:56px;
  height:21px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:18px;
  text-align:center;
}
#u470 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u470_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
.u472_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u472 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:0px;
  width:50px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u472 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u472_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u473_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u473 {
  border-width:0px;
  position:absolute;
  left:119px;
  top:0px;
  width:50px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u473 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u473_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u474_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u474 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u474 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u474_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u475_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u475 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:0px;
  width:50px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u475 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u475_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u476_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u476 {
  border-width:0px;
  position:absolute;
  left:269px;
  top:0px;
  width:50px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u476 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u476_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u477_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u477 {
  border-width:0px;
  position:absolute;
  left:319px;
  top:0px;
  width:50px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u477 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u477_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u478_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u478 {
  border-width:0px;
  position:absolute;
  left:369px;
  top:0px;
  width:50px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u478 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u478_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u479_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u479 {
  border-width:0px;
  position:absolute;
  left:419px;
  top:0px;
  width:50px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u479 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u479_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u480_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u480 {
  border-width:0px;
  position:absolute;
  left:469px;
  top:0px;
  width:50px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u480 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u480_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u471 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:7658px;
  width:519px;
  height:270px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border-radius:0px;
}
