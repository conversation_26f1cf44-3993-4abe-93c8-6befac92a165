[EMC]
VERSION = 1.1
MACHINE = LinuxCNC-DEMO-SIM-CL
# Debug level, 0 means no messages. See src/emc/nml_int/emcglb.h for others
DEBUG = 0

[DISPLAY]
DISPLAY =   axis
CYCLE_TIME = 0.200
HELP_FILE = tklinuxcnc.txt
POSITION_OFFSET = RELATIVE
POSITION_FEEDBACK = ACTUAL
MAX_FEED_OVERRIDE = 1.2
PROGRAM_PREFIX = /home/<USER>/linuxcnc/nc_files
INTRO_GRAPHIC = linuxcnc.gif
INTRO_TIME = 2
#EDITOR = geany

[RS274NGC]
PARAMETER_FILE = demo_sim_cl.var

[EMCMOT]
EMCMOT = motmod
COMM_TIMEOUT = 1.0
BASE_PERIOD = 50000
SERVO_PERIOD = 1000000

[TASK]
TASK = milltask
CYCLE_TIME = 0.010

[HAL]
HALFILE = core_sim.hal
HALFILE = demo_sim_cl.hal
HALFILE = simulated_home.hal

[TRAJ]
COORDINATES = X Y Z
LINEAR_UNITS = inch
ANGULAR_UNITS = degree
DEFAULT_LINEAR_VELOCITY = 0.0167
MAX_LINEAR_VELOCITY = 1.2
DEFAULT_LINEAR_ACCELERATION = 15.0
MAX_LINEAR_ACCELERATION = 20.0

[EMCIO]
EMCIO =   io
CYCLE_TIME = 0.100
TOOL_TABLE = demo_sim_cl.tbl

[KINS]
KINEMATICS = trivkins
JOINTS = 3

[AXIS_X]
MIN_LIMIT = -10.0
MAX_LIMIT = 10.0
MAX_VELOCITY = 1.2
MAX_ACCELERATION = 20.0

[JOINT_0]
TYPE = LINEAR
HOME = 0.000
MAX_VELOCITY = 1.2
MAX_ACCELERATION = 20.0
STEPGEN_MAXACCEL = 21.0
SCALE = 4000
MIN_LIMIT = -10.0
MAX_LIMIT = 10.0
HOME_OFFSET = 0.0
HOME_SEARCH_VEL = 20.0
HOME_LATCH_VEL = 20.0
HOME_SEQUENCE = 1
HOME_IS_SHARED = 1

[AXIS_Y]
MIN_LIMIT = -10.0
MAX_LIMIT = 10.0
MAX_VELOCITY = 1.2
MAX_ACCELERATION = 20.0

[JOINT_1]
TYPE = LINEAR
HOME = 0.000
MAX_VELOCITY = 1.2
MAX_ACCELERATION = 20.0
STEPGEN_MAXACCEL = 21.0
SCALE = 4000
MIN_LIMIT = -10.0
MAX_LIMIT = 10.0
HOME_OFFSET = 0.0
HOME_SEARCH_VEL = 20.0
HOME_LATCH_VEL = 20.0
HOME_SEQUENCE = 1

[AXIS_Z]
MIN_LIMIT = -2.0
MAX_LIMIT = 4.0
MAX_VELOCITY = 1.2
MAX_ACCELERATION = 20.0

[JOINT_2]
TYPE = LINEAR
HOME = 0.0
MAX_VELOCITY = 1.2
MAX_ACCELERATION = 20.0
STEPGEN_MAXACCEL = 21.0
SCALE = 4000
MIN_LIMIT = -2.0
MAX_LIMIT = 4.0
HOME_OFFSET = 1.0
HOME_SEARCH_VEL = 20.0
HOME_LATCH_VEL = 20.0
HOME_SEQUENCE = 0
HOME_IS_SHARED = 1
