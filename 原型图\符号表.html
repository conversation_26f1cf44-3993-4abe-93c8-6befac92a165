﻿<!DOCTYPE html>
<html>
  <head>
    <title>符号表</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/符号表/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/符号表/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- 符号表 (Group) -->
      <div id="u124" class="ax_default" data-label="符号表" data-left="0" data-top="0" data-width="1024" data-height="769" layer-opacity="1">

        <!-- Unnamed (Rectangle) -->
        <div id="u125" class="ax_default box_1 error transition notrs">
          <div id="u125_div" class="error"></div>
          <div id="u125_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u126" class="ax_default" data-left="0" data-top="0" data-width="1024" data-height="50" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u127" class="ax_default box_1 error transition notrs">
            <div id="u127_div" class="error"></div>
            <div id="u127_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Placeholder) -->
          <div id="u128" class="ax_default placeholder error transition notrs">
            <svg data="images/符号表/u128.svg" id="u128_img" class="img generatedImage">

  <defs>
    <pattern id="u128_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u128_img_cl13">
      <path d="M 0 30  L 0 0  L 30 0  L 30 30  L 0 30  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -113 -11 )">
    <path d="M 0 30  L 0 0  L 30 0  L 30 30  L 0 30  Z " fill-rule="nonzero" fill="rgba(0, 0, 0, 0)" stroke="none" transform="matrix(1 0 0 1 113 11 )" class="fill" />
    <path d="M 0 30  L 0 0  L 30 0  L 30 30  L 0 30  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 113 11 )" class="stroke" mask="url(#u128_img_cl13)" />
    <path d="M 29.646446609406727 0.35355339059327373  L 0.35355339059327373 29.646446609406727  M 0.35355339059327373 0.35355339059327373  L 29.646446609406727 29.646446609406727  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 113 11 )" class="stroke" />
  </g>
            </svg>
            <div id="u128_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u129" class="ax_default label error transition notrs">
            <div id="u129_div" class="error"></div>
            <div id="u129_text" class="text ">
              <p><span>Edit</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u130" class="ax_default" data-left="0" data-top="0.057883199296924204" data-width="1024" data-height="767.942116800703" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u131" class="ax_default box_1 error transition notrs">
            <div id="u131_div" class="error"></div>
            <div id="u131_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u132" class="ax_default button error transition notrs">
            <div id="u132_div" class="error"></div>
            <div id="u132_text" class="text ">
              <p><span>增加</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u133" class="ax_default button error transition notrs">
            <div id="u133_div" class="error"></div>
            <div id="u133_text" class="text ">
              <p><span>删除</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u134" class="ax_default button error transition notrs">
            <div id="u134_div" class="error"></div>
            <div id="u134_text" class="text ">
              <p><span>查找</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u135" class="ax_default button error transition notrs">
            <div id="u135_div" class="error"></div>
            <div id="u135_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u136" class="ax_default button error transition notrs">
            <div id="u136_div" class="error"></div>
            <div id="u136_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u137" class="ax_default button error transition notrs">
            <div id="u137_div" class="error"></div>
            <div id="u137_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u138" class="ax_default button error transition notrs">
            <div id="u138_div" class="error"></div>
            <div id="u138_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u139" class="ax_default button error transition notrs">
            <div id="u139_div" class="error"></div>
            <div id="u139_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u140" class="ax_default button error transition notrs">
            <div id="u140_div" class="error"></div>
            <div id="u140_text" class="text ">
              <p><span>更新修改</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u141" class="ax_default button error transition notrs">
            <div id="u141_div" class="error"></div>
            <div id="u141_text" class="text ">
              <p><span>放弃修改</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u142" class="ax_default button error transition notrs">
            <div id="u142_div" class="error"></div>
            <div id="u142_text" class="text ">
              <p><span>&gt;</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u143" class="ax_default button error transition notrs">
            <div id="u143_div" class="error"></div>
            <div id="u143_text" class="text ">
              <p><span>&lt;</span></p>
            </div>
          </div>

          <!-- Unnamed (Line) -->
          <div id="u144" class="ax_default line error transition notrs">
            <svg data="images/三级菜单/u22.svg" id="u144_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -66 -25 )">
    <path d="M 0 0.5  L 57 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 66 25 )" class="stroke" />
  </g>
            </svg>
            <div id="u144_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u145" class="ax_default" data-left="17" data-top="66" data-width="990" data-height="621" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u146" class="ax_default box_1 error transition notrs">
            <div id="u146_div" class="error"></div>
            <div id="u146_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Line) -->
          <div id="u147" class="ax_default line error transition notrs">
            <svg data="images/三级菜单/u25.svg" id="u147_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -16 -95 )">
    <path d="M 0 0.5  L 990 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 16 95 )" class="stroke" />
  </g>
            </svg>
            <div id="u147_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- 符号表 (Rectangle) -->
          <div id="u148" class="ax_default label error transition notrs" data-label="符号表">
            <div id="u148_div" class="error"></div>
            <div id="u148_text" class="text ">
              <p><span>符号表</span></p>
            </div>
          </div>

          <!-- Table repeater (Repeater) -->
          <div id="u149" class="ax_default" data-label="Table repeater">
            <script id="u149_script" type="axure-repeater-template" data-label="Table repeater">

              <!-- Unnamed (Rectangle) -->
              <div id="u150" class="ax_default box_1 u150 transition notrs">
                <div id="u150_div" class="u150_div"></div>
                <div id="u150_text" class="text u150_text" style="display:none; visibility: hidden">
                  <p></p>
                </div>
              </div>

              <!-- Unnamed (Rectangle) -->
              <div id="u151" class="ax_default box_1 u151 transition notrs">
                <div id="u151_div" class="u151_div"></div>
                <div id="u151_text" class="text u151_text" style="display:none; visibility: hidden">
                  <p></p>
                </div>
              </div>

              <!-- Unnamed (Rectangle) -->
              <div id="u152" class="ax_default box_1 u152 transition notrs">
                <div id="u152_div" class="u152_div"></div>
                <div id="u152_text" class="text u152_text" style="display:none; visibility: hidden">
                  <p></p>
                </div>
              </div>
            </script>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
