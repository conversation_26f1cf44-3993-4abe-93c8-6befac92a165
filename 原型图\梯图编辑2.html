﻿<!DOCTYPE html>
<html>
  <head>
    <title>梯图编辑2</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/梯图编辑2/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/梯图编辑2/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- 梯图编辑2 (Group) -->
      <div id="u98" class="ax_default" data-label="梯图编辑2" data-left="0" data-top="0" data-width="1024" data-height="769" layer-opacity="1">

        <!-- Unnamed (Rectangle) -->
        <div id="u99" class="ax_default box_1 error transition notrs">
          <div id="u99_div" class="error"></div>
          <div id="u99_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u100" class="ax_default" data-left="0" data-top="0" data-width="1024" data-height="50" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u101" class="ax_default box_1 error transition notrs">
            <div id="u101_div" class="error"></div>
            <div id="u101_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Placeholder) -->
          <div id="u102" class="ax_default placeholder error transition notrs">
            <svg data="images/梯图编辑2/u102.svg" id="u102_img" class="img generatedImage">

  <defs>
    <pattern id="u102_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u102_img_cl12">
      <path d="M 0 30  L 0 0  L 30 0  L 30 30  L 0 30  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -113 -11 )">
    <path d="M 0 30  L 0 0  L 30 0  L 30 30  L 0 30  Z " fill-rule="nonzero" fill="rgba(0, 0, 0, 0)" stroke="none" transform="matrix(1 0 0 1 113 11 )" class="fill" />
    <path d="M 0 30  L 0 0  L 30 0  L 30 30  L 0 30  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 113 11 )" class="stroke" mask="url(#u102_img_cl12)" />
    <path d="M 29.646446609406727 0.35355339059327373  L 0.35355339059327373 29.646446609406727  M 0.35355339059327373 0.35355339059327373  L 29.646446609406727 29.646446609406727  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 113 11 )" class="stroke" />
  </g>
            </svg>
            <div id="u102_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u103" class="ax_default label error transition notrs">
            <div id="u103_div" class="error"></div>
            <div id="u103_text" class="text ">
              <p><span>Edit</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u104" class="ax_default" data-left="0" data-top="0.057883199296924204" data-width="1024" data-height="767.942116800703" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u105" class="ax_default box_1 error transition notrs">
            <div id="u105_div" class="error"></div>
            <div id="u105_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u106" class="ax_default button error transition notrs">
            <div id="u106_div" class="error"></div>
            <div id="u106_text" class="text ">
              <p><span>查找</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u107" class="ax_default button error transition notrs">
            <div id="u107_div" class="error"></div>
            <div id="u107_text" class="text ">
              <p><span>删除网络</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u108" class="ax_default button error transition notrs">
            <div id="u108_div" class="error"></div>
            <div id="u108_text" class="text ">
              <p><span>插入网络</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u109" class="ax_default button error transition notrs">
            <div id="u109_div" class="error"></div>
            <div id="u109_text" class="text ">
              <p><span>修改注释</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u110" class="ax_default button error transition notrs">
            <div id="u110_div" class="error"></div>
            <div id="u110_text" class="text ">
              <p><span>插入行</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u111" class="ax_default button error transition notrs">
            <div id="u111_div" class="error"></div>
            <div id="u111_text" class="text ">
              <p><span>删除行</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u112" class="ax_default button error transition notrs">
            <div id="u112_div" class="error"></div>
            <div id="u112_text" class="text ">
              <p><span>双线圈</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u113" class="ax_default button error transition notrs">
            <div id="u113_div" class="error"></div>
            <div id="u113_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u114" class="ax_default button error transition notrs">
            <div id="u114_div" class="error"></div>
            <div id="u114_text" class="text ">
              <p><span>更新修改</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u115" class="ax_default button error transition notrs">
            <div id="u115_div" class="error"></div>
            <div id="u115_text" class="text ">
              <p><span>放弃修改</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u116" class="ax_default button error transition notrs">
            <div id="u116_div" class="error"></div>
            <div id="u116_text" class="text ">
              <p><span>&gt;</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u117" class="ax_default button error transition notrs">
            <div id="u117_div" class="error"></div>
            <div id="u117_text" class="text ">
              <p><span>&lt;</span></p>
            </div>
          </div>

          <!-- Unnamed (Line) -->
          <div id="u118" class="ax_default line error transition notrs">
            <svg data="images/三级菜单/u22.svg" id="u118_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -66 -25 )">
    <path d="M 0 0.5  L 57 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 66 25 )" class="stroke" />
  </g>
            </svg>
            <div id="u118_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u119" class="ax_default" data-left="17" data-top="66" data-width="990" data-height="621" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u120" class="ax_default box_1 error transition notrs">
            <div id="u120_div" class="error"></div>
            <div id="u120_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Line) -->
          <div id="u121" class="ax_default line error transition notrs">
            <svg data="images/三级菜单/u25.svg" id="u121_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -16 -95 )">
    <path d="M 0 0.5  L 990 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 16 95 )" class="stroke" />
  </g>
            </svg>
            <div id="u121_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u122" class="ax_default label error transition notrs">
            <div id="u122_div" class="error"></div>
            <div id="u122_text" class="text ">
              <p><span>梯图编辑</span></p>
            </div>
          </div>

          <!-- Unnamed (Image) -->
          <div id="u123" class="ax_default image error transition notrs">
            <img id="u123_img" class="img " src="images/梯图编辑1/u97.png"/>
            <div id="u123_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
