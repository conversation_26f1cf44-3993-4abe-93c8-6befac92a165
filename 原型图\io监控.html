﻿<!DOCTYPE html>
<html>
  <head>
    <title>IO监控</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/io监控/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/io监控/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- IO监控 (Group) -->
      <div id="u182" class="ax_default" data-label="IO监控" data-left="0" data-top="0" data-width="1024" data-height="768" layer-opacity="1">

        <!-- Unnamed (Rectangle) -->
        <div id="u183" class="ax_default box_1 error transition notrs">
          <div id="u183_div" class="error"></div>
          <div id="u183_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u184" class="ax_default" data-left="0" data-top="0" data-width="1024" data-height="50" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u185" class="ax_default box_1 error transition notrs">
            <div id="u185_div" class="error"></div>
            <div id="u185_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Placeholder) -->
          <div id="u186" class="ax_default placeholder error transition notrs">
            <svg data="images/io监控/u186.svg" id="u186_img" class="img generatedImage">

  <defs>
    <pattern id="u186_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u186_img_cl15">
      <path d="M 0 30  L 0 0  L 30 0  L 30 30  L 0 30  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -113 -11 )">
    <path d="M 0 30  L 0 0  L 30 0  L 30 30  L 0 30  Z " fill-rule="nonzero" fill="rgba(0, 0, 0, 0)" stroke="none" transform="matrix(1 0 0 1 113 11 )" class="fill" />
    <path d="M 0 30  L 0 0  L 30 0  L 30 30  L 0 30  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 113 11 )" class="stroke" mask="url(#u186_img_cl15)" />
    <path d="M 29.646446609406727 0.35355339059327373  L 0.35355339059327373 29.646446609406727  M 0.35355339059327373 0.35355339059327373  L 29.646446609406727 29.646446609406727  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 113 11 )" class="stroke" />
  </g>
            </svg>
            <div id="u186_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u187" class="ax_default label error transition notrs">
            <div id="u187_div" class="error"></div>
            <div id="u187_text" class="text ">
              <p><span>Edit</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u188" class="ax_default" data-left="0" data-top="0.057883199296924204" data-width="1024" data-height="766.942116800703" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u189" class="ax_default button error transition notrs">
            <div id="u189_div" class="error"></div>
            <div id="u189_text" class="text ">
              <p><span>查找</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u190" class="ax_default button error transition notrs">
            <div id="u190_div" class="error"></div>
            <div id="u190_text" class="text ">
              <p><span>X变量</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u191" class="ax_default button error transition notrs">
            <div id="u191_div" class="error"></div>
            <div id="u191_text" class="text ">
              <p><span>Y变量</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u192" class="ax_default button error transition notrs">
            <div id="u192_div" class="error"></div>
            <div id="u192_text" class="text ">
              <p><span>I变量</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u193" class="ax_default button error transition notrs">
            <div id="u193_div" class="error"></div>
            <div id="u193_text" class="text ">
              <p><span>O变量</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u194" class="ax_default button error transition notrs">
            <div id="u194_div" class="error"></div>
            <div id="u194_text" class="text ">
              <p><span>F变量</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u195" class="ax_default button error transition notrs">
            <div id="u195_div" class="error"></div>
            <div id="u195_text" class="text ">
              <p><span>G变量</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u196" class="ax_default button error transition notrs">
            <div id="u196_div" class="error"></div>
            <div id="u196_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u197" class="ax_default button error transition notrs">
            <div id="u197_div" class="error"></div>
            <div id="u197_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u198" class="ax_default button error transition notrs">
            <div id="u198_div" class="error"></div>
            <div id="u198_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u199" class="ax_default button error transition notrs">
            <div id="u199_div" class="error"></div>
            <div id="u199_text" class="text ">
              <p><span>&gt;</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u200" class="ax_default button error transition notrs">
            <div id="u200_div" class="error"></div>
            <div id="u200_text" class="text ">
              <p><span>&lt;</span></p>
            </div>
          </div>

          <!-- Unnamed (Line) -->
          <div id="u201" class="ax_default line error transition notrs">
            <svg data="images/三级菜单/u22.svg" id="u201_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -66 -25 )">
    <path d="M 0 0.5  L 57 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 66 25 )" class="stroke" />
  </g>
            </svg>
            <div id="u201_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u202" class="ax_default" data-left="17" data-top="66" data-width="990" data-height="620" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u203" class="ax_default box_1 error transition notrs">
            <div id="u203_div" class="error"></div>
            <div id="u203_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Line) -->
          <div id="u204" class="ax_default line error transition notrs">
            <svg data="images/三级菜单/u25.svg" id="u204_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -16 -95 )">
    <path d="M 0 0.5  L 990 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 16 95 )" class="stroke" />
  </g>
            </svg>
            <div id="u204_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u205" class="ax_default label error transition notrs">
            <div id="u205_div" class="error"></div>
            <div id="u205_text" class="text ">
              <p><span>IO监控</span></p>
            </div>
          </div>
        </div>

        <!-- Table repeater (Repeater) -->
        <div id="u206" class="ax_default" data-label="Table repeater">
          <script id="u206_script" type="axure-repeater-template" data-label="Table repeater">

            <!-- Unnamed (Rectangle) -->
            <div id="u207" class="ax_default box_1 u207 transition notrs">
              <div id="u207_div" class="u207_div"></div>
              <div id="u207_text" class="text u207_text" style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (Rectangle) -->
            <div id="u208" class="ax_default box_1 u208 transition notrs">
              <div id="u208_div" class="u208_div"></div>
              <div id="u208_text" class="text u208_text" style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (Rectangle) -->
            <div id="u209" class="ax_default box_1 u209 transition notrs">
              <div id="u209_div" class="u209_div"></div>
              <div id="u209_text" class="text u209_text" style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (Rectangle) -->
            <div id="u210" class="ax_default box_1 u210 transition notrs">
              <div id="u210_div" class="u210_div"></div>
              <div id="u210_text" class="text u210_text" style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (Rectangle) -->
            <div id="u211" class="ax_default box_1 u211 transition notrs">
              <div id="u211_div" class="u211_div"></div>
              <div id="u211_text" class="text u211_text" style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (Rectangle) -->
            <div id="u212" class="ax_default box_1 u212 transition notrs">
              <div id="u212_div" class="u212_div"></div>
              <div id="u212_text" class="text u212_text" style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (Rectangle) -->
            <div id="u213" class="ax_default box_1 u213 transition notrs">
              <div id="u213_div" class="u213_div"></div>
              <div id="u213_text" class="text u213_text" style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (Rectangle) -->
            <div id="u214" class="ax_default box_1 u214 transition notrs">
              <div id="u214_div" class="u214_div"></div>
              <div id="u214_text" class="text u214_text" style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (Rectangle) -->
            <div id="u215" class="ax_default box_1 u215 transition notrs">
              <div id="u215_div" class="u215_div"></div>
              <div id="u215_text" class="text u215_text" style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </script>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
