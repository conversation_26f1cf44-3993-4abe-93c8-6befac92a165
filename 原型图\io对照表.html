﻿<!DOCTYPE html>
<html>
  <head>
    <title>IO对照表</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/io对照表/styles.css" type="text/css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.7.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/io对照表/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- IO对照表 (Group) -->
      <div id="u153" class="ax_default" data-label="IO对照表" data-left="0" data-top="0" data-width="1024" data-height="769" layer-opacity="1">

        <!-- Unnamed (Rectangle) -->
        <div id="u154" class="ax_default box_1 error transition notrs">
          <div id="u154_div" class="error"></div>
          <div id="u154_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u155" class="ax_default" data-left="0" data-top="0" data-width="1024" data-height="50" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u156" class="ax_default box_1 error transition notrs">
            <div id="u156_div" class="error"></div>
            <div id="u156_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Placeholder) -->
          <div id="u157" class="ax_default placeholder error transition notrs">
            <svg data="images/io对照表/u157.svg" id="u157_img" class="img generatedImage">

  <defs>
    <pattern id="u157_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imageRepeat="None" />
    <mask fill="white" id="u157_img_cl14">
      <path d="M 0 30  L 0 0  L 30 0  L 30 30  L 0 30  Z " fill-rule="evenodd" />
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -113 -11 )">
    <path d="M 0 30  L 0 0  L 30 0  L 30 30  L 0 30  Z " fill-rule="nonzero" fill="rgba(0, 0, 0, 0)" stroke="none" transform="matrix(1 0 0 1 113 11 )" class="fill" />
    <path d="M 0 30  L 0 0  L 30 0  L 30 30  L 0 30  Z " stroke-width="2" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 113 11 )" class="stroke" mask="url(#u157_img_cl14)" />
    <path d="M 29.646446609406727 0.35355339059327373  L 0.35355339059327373 29.646446609406727  M 0.35355339059327373 0.35355339059327373  L 29.646446609406727 29.646446609406727  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 113 11 )" class="stroke" />
  </g>
            </svg>
            <div id="u157_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u158" class="ax_default label error transition notrs">
            <div id="u158_div" class="error"></div>
            <div id="u158_text" class="text ">
              <p><span>Edit</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u159" class="ax_default" data-left="0" data-top="0.057883199296924204" data-width="1024" data-height="767.942116800703" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u160" class="ax_default box_1 error transition notrs">
            <div id="u160_div" class="error"></div>
            <div id="u160_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u161" class="ax_default button error transition notrs">
            <div id="u161_div" class="error"></div>
            <div id="u161_text" class="text ">
              <p><span>增加</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u162" class="ax_default button error transition notrs">
            <div id="u162_div" class="error"></div>
            <div id="u162_text" class="text ">
              <p><span>删除</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u163" class="ax_default button error transition notrs">
            <div id="u163_div" class="error"></div>
            <div id="u163_text" class="text ">
              <p><span>查找</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u164" class="ax_default button error transition notrs">
            <div id="u164_div" class="error"></div>
            <div id="u164_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u165" class="ax_default button error transition notrs">
            <div id="u165_div" class="error"></div>
            <div id="u165_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u166" class="ax_default button error transition notrs">
            <div id="u166_div" class="error"></div>
            <div id="u166_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u167" class="ax_default button error transition notrs">
            <div id="u167_div" class="error"></div>
            <div id="u167_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u168" class="ax_default button error transition notrs">
            <div id="u168_div" class="error"></div>
            <div id="u168_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u169" class="ax_default button error transition notrs">
            <div id="u169_div" class="error"></div>
            <div id="u169_text" class="text ">
              <p><span>更新修改</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u170" class="ax_default button error transition notrs">
            <div id="u170_div" class="error"></div>
            <div id="u170_text" class="text ">
              <p><span>放弃修改</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u171" class="ax_default button error transition notrs">
            <div id="u171_div" class="error"></div>
            <div id="u171_text" class="text ">
              <p><span>&gt;</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u172" class="ax_default button error transition notrs">
            <div id="u172_div" class="error"></div>
            <div id="u172_text" class="text ">
              <p><span>&lt;</span></p>
            </div>
          </div>

          <!-- Unnamed (Line) -->
          <div id="u173" class="ax_default line error transition notrs">
            <svg data="images/三级菜单/u22.svg" id="u173_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -66 -25 )">
    <path d="M 0 0.5  L 57 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 66 25 )" class="stroke" />
  </g>
            </svg>
            <div id="u173_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u174" class="ax_default" data-left="17" data-top="66" data-width="990" data-height="621" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u175" class="ax_default box_1 error transition notrs">
            <div id="u175_div" class="error"></div>
            <div id="u175_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (Line) -->
          <div id="u176" class="ax_default line error transition notrs">
            <svg data="images/三级菜单/u25.svg" id="u176_img" class="img generatedImage">

  <g transform="matrix(1 0 0 1 -16 -95 )">
    <path d="M 0 0.5  L 990 0.5  " stroke-width="1" stroke-dasharray="0" stroke="rgba(121, 121, 121, 1)" fill="none" transform="matrix(1 0 0 1 16 95 )" class="stroke" />
  </g>
            </svg>
            <div id="u176_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- IO对照表 (Rectangle) -->
          <div id="u177" class="ax_default label error transition notrs" data-label="IO对照表">
            <div id="u177_div" class="error"></div>
            <div id="u177_text" class="text ">
              <p><span>IO对照表</span></p>
            </div>
          </div>

          <!-- Table repeater (Repeater) -->
          <div id="u178" class="ax_default" data-label="Table repeater">
            <script id="u178_script" type="axure-repeater-template" data-label="Table repeater">

              <!-- Unnamed (Rectangle) -->
              <div id="u179" class="ax_default box_1 u179 transition notrs">
                <div id="u179_div" class="u179_div"></div>
                <div id="u179_text" class="text u179_text" style="display:none; visibility: hidden">
                  <p></p>
                </div>
              </div>

              <!-- Unnamed (Rectangle) -->
              <div id="u180" class="ax_default box_1 u180 transition notrs">
                <div id="u180_div" class="u180_div"></div>
                <div id="u180_text" class="text u180_text" style="display:none; visibility: hidden">
                  <p></p>
                </div>
              </div>

              <!-- Unnamed (Rectangle) -->
              <div id="u181" class="ax_default box_1 u181 transition notrs">
                <div id="u181_div" class="u181_div"></div>
                <div id="u181_text" class="text u181_text" style="display:none; visibility: hidden">
                  <p></p>
                </div>
              </div>
            </script>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
