﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-0px;
  width:1024px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u153 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u154_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:768px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u154 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:1px;
  width:1024px;
  height:768px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u154 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u154_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u155 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u156_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u156 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u156 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u156_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u157 {
  border-width:0px;
  position:absolute;
  left:113px;
  top:11px;
  width:30px;
  height:30px;
  display:flex;
  transition:none;
}
#u157 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u157_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u157_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u158_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:30px;
  text-align:center;
}
#u158 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:50px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:30px;
  text-align:center;
}
#u158 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u158_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u159 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u160_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u160 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:703px;
  width:1024px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u160 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u160_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u161_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u161 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u161 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u161_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u162_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u162 {
  border-width:0px;
  position:absolute;
  left:132px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u162 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u162_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u163_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u163 {
  border-width:0px;
  position:absolute;
  left:227px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u163 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u163_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u164_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u164 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u164 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u164_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u165_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u165 {
  border-width:0px;
  position:absolute;
  left:417px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u165 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u165_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u166_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u166 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u166 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u166_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u167_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u167 {
  border-width:0px;
  position:absolute;
  left:607px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u167 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u167_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u168_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u168 {
  border-width:0px;
  position:absolute;
  left:702px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u168 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u168_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u169_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u169 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u169 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u169_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u170_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u170 {
  border-width:0px;
  position:absolute;
  left:892px;
  top:703px;
  width:95px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u170 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u170_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u171_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u171 {
  border-width:0px;
  position:absolute;
  left:987px;
  top:703px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u171 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u171_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u172_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:65px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  filter:drop-shadow(none);
  transition:none;
}
#u172 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:703px;
  width:37px;
  height:65px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u172 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u172_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u173 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:25px;
  width:57px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-62.19271366293822deg);
  -moz-transform:rotate(-62.19271366293822deg);
  -ms-transform:rotate(-62.19271366293822deg);
  transform:rotate(-62.19271366293822deg);
  transition:none;
}
#u173 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u173_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:58px;
  height:2px;
}
#u173_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u174 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u175_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:990px;
  height:621px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.9921568627450981);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  filter:drop-shadow(none);
  transition:none;
  color:transparent;
}
#u175 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:66px;
  width:990px;
  height:621px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:transparent;
}
#u175 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u175_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u176 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:96px;
  width:990px;
  height:1px;
  display:flex;
  transition:none;
}
#u176 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u176_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:991px;
  height:2px;
}
#u176_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u177_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-size:18px;
  text-align:center;
}
#u177 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:72px;
  width:74px;
  height:21px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-size:18px;
  text-align:center;
}
#u177 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u177_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
.u179_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u179 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:0px;
  width:200px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u179 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u179_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u180_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u180 {
  border-width:0px;
  position:absolute;
  left:150px;
  top:0px;
  width:150px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u180 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u180_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u181_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(51, 51, 51, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  color:#555555;
  text-align:left;
}
.u181 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  color:#555555;
  text-align:left;
}
.u181 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
.u181_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u178 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:98px;
  width:500px;
  height:150px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border-radius:0px;
}
